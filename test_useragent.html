<!DOCTYPE html>
<html>
<head>
    <title>User Agent Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .ua-display {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #4CAF50;
        }
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .refresh-btn:hover {
            background: #45a049;
        }
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .timestamp {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 10px;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.changed {
            background: #4CAF50;
        }
        .status.default {
            background: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 User Agent Test Page</h1>
        
        <div class="info-box">
            <h3>How to Test:</h3>
            <ol>
                <li>Note the current user agent below</li>
                <li>Open the User Agent Switcher extension popup</li>
                <li>Click the floating "AUTO" button in the top-right corner</li>
                <li>Come back to this page and click "Refresh Page" or "Check Again"</li>
                <li>The user agent should have changed!</li>
            </ol>
        </div>

        <h3>Current User Agent:</h3>
        <div class="ua-display" id="current-ua"></div>
        
        <div>
            <span class="status" id="status">Default</span>
            <div class="timestamp" id="timestamp"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="refresh-btn" onclick="checkUserAgent()">🔄 Check Again</button>
            <button class="refresh-btn" onclick="location.reload()">🔃 Refresh Page</button>
            <button class="refresh-btn" onclick="openExtension()">🔧 Open Extension</button>
        </div>

        <div class="info-box">
            <h4>Expected Behavior:</h4>
            <ul>
                <li>After clicking the AUTO button, this page should show a different user agent</li>
                <li>The change applies to ALL tabs (not just this one)</li>
                <li>The extension automatically sets to blacklist mode for maximum compatibility</li>
                <li>New tabs opened will also use the new user agent</li>
            </ul>
        </div>
    </div>

    <script>
        let originalUA = navigator.userAgent;
        
        function checkUserAgent() {
            const currentUA = navigator.userAgent;
            const uaElement = document.getElementById('current-ua');
            const statusElement = document.getElementById('status');
            const timestampElement = document.getElementById('timestamp');
            
            uaElement.textContent = currentUA;
            
            if (currentUA !== originalUA) {
                statusElement.textContent = 'Changed';
                statusElement.className = 'status changed';
            } else {
                statusElement.textContent = 'Default';
                statusElement.className = 'status default';
            }
            
            timestampElement.textContent = 'Last checked: ' + new Date().toLocaleTimeString();
        }
        
        function openExtension() {
            // This won't work from a regular webpage, but provides instruction
            alert('Please click on the User Agent Switcher extension icon in your browser toolbar to open the popup.');
        }
        
        // Initial check
        checkUserAgent();
        
        // Auto-refresh every 5 seconds to catch changes
        setInterval(checkUserAgent, 5000);
    </script>
</body>
</html>
