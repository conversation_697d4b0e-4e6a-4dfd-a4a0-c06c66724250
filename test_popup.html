<!DOCTYPE html>
<html>
<head>
    <title>Test User Agent Switcher Popup</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .popup-container {
            border: 2px solid #ccc;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
            display: inline-block;
        }
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background: #e7f3ff;
            border-radius: 4px;
        }
        .current-ua {
            margin-top: 20px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>User Agent Switcher Test</h1>
        
        <div class="info">
            <h3>Testing the Floating Auto Switch Button</h3>
            <p>This page loads the popup interface to test the new floating button functionality.</p>
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Look for the floating "AUTO" button in the top-right corner of the popup</li>
                <li>Check if the button has a green status indicator (means ready for all tabs)</li>
                <li>Click the button to automatically switch to a random user agent</li>
                <li>The button should show a pulsing animation while switching</li>
                <li>A success message should appear</li>
                <li>The user agent field should be populated with a new random user agent</li>
                <li>The extension will be set to blacklist mode with empty blacklist (applies to all tabs)</li>
                <li>The current tab will be automatically refreshed to show changes</li>
            </ol>
            <p><strong>Note:</strong> The user agent change applies to ALL tabs when using the auto-switch button!</p>
        </div>

        <div class="popup-container">
            <iframe src="data/popup/index.html" width="620" height="500" frameborder="0"></iframe>
        </div>

        <div class="current-ua">
            <h3>Current User Agent:</h3>
            <p id="current-ua-text"></p>
        </div>
    </div>

    <script>
        // Display current user agent
        document.getElementById('current-ua-text').textContent = navigator.userAgent;
        
        // Update user agent display every 2 seconds to catch changes
        setInterval(() => {
            document.getElementById('current-ua-text').textContent = navigator.userAgent;
        }, 2000);
    </script>
</body>
</html>
