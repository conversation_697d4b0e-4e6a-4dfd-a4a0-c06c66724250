/* global Agent */
'use strict';

// localization
document.querySelectorAll('[data-localized-value]').forEach(e => {
  const ref = e.dataset.localizedValue;
  const translated = chrome.i18n.getMessage(ref);
  if (translated) {
    e.value = translated;
  }
});
document.querySelectorAll('[data-localized-title]').forEach(e => {
  const ref = e.dataset.localizedTitle;
  const translated = chrome.i18n.getMessage(ref);
  if (translated) {
    e.title = translated;
  }
});
document.querySelectorAll('[data-localized-placeholder]').forEach(e => {
  const ref = e.dataset.localizedPlaceholder;
  const translated = chrome.i18n.getMessage(ref);
  if (translated) {
    e.placeholder = translated;
  }
});
document.querySelectorAll('[data-localized-content]').forEach(e => {
  const ref = e.dataset.localizedContent;
  const translated = chrome.i18n.getMessage(ref);
  if (translated) {
    e.dataset.content = translated;
  }
});
document.querySelectorAll('[data-localize]').forEach(e => {
  const ref = e.dataset.localize;
  const translated = chrome.i18n.getMessage(ref);
  if (translated) {
    e.textContent = translated;
  }
});

document.body.dataset.android = navigator.userAgent.indexOf('Android') !== -1;

const map = {};

function sort(arr) {
  function sort(a = '', b = '') {
    const pa = a.split('.');
    const pb = b.split('.');
    for (let i = 0; i < 3; i++) {
      const na = Number(pa[i]);
      const nb = Number(pb[i]);
      if (na > nb) {
        return 1;
      }
      if (nb > na) {
        return -1;
      }
      if (!isNaN(na) && isNaN(nb)) {
        return 1;
      }
      if (isNaN(na) && !isNaN(nb)) {
        return -1;
      }
    }
    return 0;
  }
  const list = arr.sort((a, b) => sort(a.browser.version, b.browser.version));
  if (document.getElementById('sort').value === 'descending') {
    return list.reverse();
  }
  return list;
}

function get(path) {
  const cf = Promise.resolve({
    match() {
      return Promise.resolve();
    },
    add() {
      return Promise.resolve();
    }
  });
  return (typeof caches !== 'undefined' ? caches : {
    open() {
      return cf;
    }
  }).open('agents').catch(() => cf).then(cache => {
    const link = 'https://cdn.jsdelivr.net/gh/ray-lothian/UserAgent-Switcher@latest/v3/data/popup/' + path;
    // updating agents once per day
    chrome.storage.local.get({
      ['cache.' + path]: 0
    }, prefs => {
      const now = Date.now();
      if (now - prefs['cache.' + path] > 1 * 24 * 60 * 60 * 1000) {
        cache.add(link).then(() => chrome.storage.local.set({
          ['cache.' + path]: now
        }));
      }
    });
    return cache.match(link).then(resp => resp || fetch(path));
  });
}

function update(ua) {
  const browser = document.getElementById('browser').value;
  const os = document.getElementById('os').value;

  const t = document.querySelector('template');
  const parent = document.getElementById('list');
  const tbody = parent.querySelector('tbody');
  tbody.textContent = '';

  parent.dataset.loading = true;
  get('browsers/' + browser.toLowerCase() + '-' + os.toLowerCase().replace(/\//g, '-') + '.json')
    .then(r => r.json()).catch(e => {
      console.error('CACHE_ERROR', e);
      return [];
    }).then(list => {
      if (list) {
        const fragment = document.createDocumentFragment();
        let radio;
        list = sort(list);
        list.forEach((o, n) => {
          const clone = document.importNode(t.content, true);
          const num = clone.querySelector('td:nth-child(1)');
          num.textContent = n + 1;
          const second = clone.querySelector('td:nth-child(3)');
          if (o.browser.name && o.browser.version) {
            second.title = second.textContent = o.browser.name + ' ' + (o.browser.version || ' ');
          }
          else {
            second.title = second.textContent = '-';
          }
          const third = clone.querySelector('td:nth-child(4)');
          if (o.os.name && o.os.version) {
            third.title = third.textContent = o.os.name + ' ' + (o.os.version || ' ');
          }
          else {
            third.title = third.textContent = '-';
          }
          const forth = clone.querySelector('td:nth-child(5)');
          forth.title = forth.textContent = o.ua;
          if (o.ua === ua) {
            radio = clone.querySelector('input[type=radio]');
          }
          fragment.appendChild(clone);
        });
        tbody.appendChild(fragment);
        if (radio) {
          radio.checked = true;
          radio.scrollIntoView({
            block: 'center',
            inline: 'nearest'
          });
        }
        document.getElementById('custom').placeholder = chrome.i18n.getMessage('filterAmong', [list.length]);
        [...document.getElementById('os').querySelectorAll('option')].forEach(option => {
          option.disabled = (map.matching[browser.toLowerCase()] || []).indexOf(option.value.toLowerCase()) === -1;
        });
      }
      else {
        throw Error('OS is not found');
      }
    // FF 55.0 does not support finally
    }).catch(() => {}).then(() => {
      parent.dataset.loading = false;
    });
}

document.getElementById('browser').addEventListener('change', e => chrome.storage.local.set({
  'popup-browser': e.target.value
}));
document.getElementById('os').addEventListener('change', e => chrome.storage.local.set({
  'popup-os': e.target.value
}));
document.getElementById('sort').addEventListener('change', e => chrome.storage.local.set({
  'popup-sort': e.target.value
}));

document.addEventListener('change', ({target}) => {
  if (target.closest('#filter')) {
    chrome.storage.local.get({
      ua: ''
    }, prefs => update(prefs.ua || navigator.userAgent));
  }
  if (target.type === 'radio') {
    document.getElementById('ua').value = target.closest('tr').querySelector('td:nth-child(5)').textContent;
    document.getElementById('ua').dispatchEvent(new Event('input'));
  }
});

document.addEventListener('DOMContentLoaded', () => fetch('./map.json').then(r => r.json()).then(o => {
  Object.assign(map, o);

  const f1 = document.createDocumentFragment();
  for (const browser of map.browser) {
    const option = document.createElement('option');
    option.value = option.textContent = browser;
    f1.appendChild(option);
  }
  const f2 = document.createDocumentFragment();
  for (const os of map.os) {
    const option = document.createElement('option');
    option.value = option.textContent = os;
    f2.appendChild(option);
  }

  document.querySelector('#browser optgroup:last-of-type').appendChild(f1);
  document.querySelector('#os optgroup:last-of-type').appendChild(f2);

  chrome.storage.local.get({
    'popup-browser': 'Chrome',
    'popup-os': 'Windows',
    'popup-sort': 'descending',
    'ua': ''
  }, prefs => {
    document.getElementById('browser').value = prefs['popup-browser'];
    document.getElementById('os').value = prefs['popup-os'];
    document.getElementById('sort').value = prefs['popup-sort'];

    update(prefs.ua);
    document.getElementById('ua').value = prefs.ua;
    document.getElementById('ua').dispatchEvent(new Event('input'));
  });
}));

document.getElementById('list').addEventListener('click', ({target}) => {
  const tr = target.closest('tbody tr');
  if (tr) {
    const input = tr.querySelector('input');
    if (input && input !== target) {
      input.checked = true;
      input.dispatchEvent(new Event('change', {
        bubbles: true
      }));
    }
  }
});

document.getElementById('custom').addEventListener('keyup', ({target}) => {
  const value = target.value;
  [...document.querySelectorAll('#list tbody tr')]
    .forEach(tr => tr.dataset.matched = tr.textContent.toLowerCase().indexOf(value.toLowerCase()) !== -1);
});

chrome.storage.onChanged.addListener(prefs => {
  if (prefs.ua) {
    document.getElementById('ua').value = prefs.ua.newValue || navigator.userAgent;
    document.getElementById('ua').dispatchEvent(new Event('input'));
  }
});

function msg(msg) {
  const toast = document.getElementById('toast');
  toast.textContent = msg;
  window.setTimeout(() => toast.textContent = '', 2000);
}

// commands
document.addEventListener('click', ({target}) => {
  const cmd = target.dataset.cmd;
  if (cmd) {
    if (cmd === 'apply') {
      const value = document.getElementById('ua').value;
      if (value === navigator.userAgent) {
        msg(chrome.i18n.getMessage('msgDefaultUA'));
      }
      else {
        msg(chrome.i18n.getMessage('msgUASet'));
      }
      if (value !== navigator.userAgent) {
        chrome.storage.local.set({
          ua: value
        });
      }
    }
    else if (cmd === 'reset') {
      const input = document.querySelector('#list :checked');
      if (input) {
        input.checked = false;
      }
      chrome.storage.local.set({
        ua: ''
      });
      msg(chrome.i18n.getMessage('msgDisabled'));
    }
    else if (cmd === 'refresh') {
      chrome.tabs.query({
        active: true,
        currentWindow: true
      }, ([tab]) => chrome.tabs.reload(tab.id, {
        bypassCache: true
      }));
    }
    else if (cmd === 'options') {
      chrome.runtime.openOptionsPage();
    }
    else if (cmd === 'reload') {
      chrome.runtime.reload();
    }
    else if (cmd === 'tab') {
      chrome.tabs.query({
        active: true,
        lastFocusedWindow: true
      }).then(([tab]) => {
        const value = document.getElementById('ua').value;
        chrome.storage.session.set({
          [tab.id]: {
            ua: value
          }
        });
      });
    }
    else if (cmd === 'test') {
      chrome.storage.local.get({
        'test': 'https://webbrowsertools.com/useragent/?method=normal&verbose=false'
      }, prefs => chrome.tabs.create({
        url: prefs.test
      }));
    }
    else if (cmd === 'debug-auto') {
      // Debug the auto-switch functionality
      console.log('=== DEBUG AUTO SWITCH ===');
      const listElement = document.getElementById('list');
      const userAgentRows = document.querySelectorAll('#list tbody tr');

      console.log('List element:', listElement);
      console.log('List loading state:', listElement?.dataset.loading);
      console.log('Number of UA rows:', userAgentRows.length);
      console.log('Recently used UAs:', recentlyUsedUAs);

      if (userAgentRows.length > 0) {
        console.log('First few UAs in list:');
        for (let i = 0; i < Math.min(5, userAgentRows.length); i++) {
          const uaCell = userAgentRows[i].querySelector('td:nth-child(5)');
          console.log(`  ${i + 1}: ${uaCell?.textContent?.substring(0, 80)}...`);
        }
      }

      // Test getting a random UA
      const testUA = getRandomUserAgent();
      console.log('Test random UA:', testUA);

      msg('Debug info logged to console (F12)');
    }

    if (cmd) {
      target.classList.add('active');
      setTimeout(() => target.classList.remove('active'), 500);
    }
  }
});

document.getElementById('ua').addEventListener('input', e => {
  const value = e.target.value;
  document.querySelector('[data-cmd=apply]').disabled = value === '';
  document.querySelector('[data-cmd=tab]').disabled = value === '';

  if (value) {
    const agent = new Agent();

    chrome.storage.local.get({
      'userAgentData': true,
      'parser': {} // maps ua string to a ua object
    }, prefs => {
      agent.prefs(prefs);
      const o = agent.parse(value);

      document.getElementById('platform').value = o.platform;
      document.getElementById('vendor').value = o.vendor;
      document.getElementById('product').value = o.product;
      document.getElementById('oscpu').value = o.oscpu;
    });
  }
});
document.getElementById('ua').addEventListener('keyup', e => {
  if (e.key === 'Enter') {
    document.querySelector('[data-cmd="apply"]').click();
  }
});

// Auto Switch User Agent Functionality
let availableUserAgents = [];
let isAutoSwitching = false;
let recentlyUsedUAs = []; // Track recently used UAs to avoid repetition

function getRandomUserAgent() {
  // Get all available user agents from the current list
  const userAgentRows = document.querySelectorAll('#list tbody tr');
  const agents = [];

  userAgentRows.forEach(row => {
    const uaCell = row.querySelector('td:nth-child(5)');
    if (uaCell && uaCell.textContent.trim()) {
      agents.push(uaCell.textContent.trim());
    }
  });

  console.log('Available agents from list:', agents.length);

  if (agents.length === 0) {
    // Fallback to diverse popular user agents if no list is loaded
    const fallbackAgents = [
      // Chrome Windows
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      // Chrome Mac
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      // Firefox Windows
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
      // Firefox Mac
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0',
      // Safari Mac
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
      // Edge Windows
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
      // Chrome Linux
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      // Firefox Linux
      'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (X11; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',
      // Mobile Chrome
      'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1'
    ];
    console.log('Using fallback agents, total:', fallbackAgents.length);

    // Filter out recently used fallback agents too
    let filteredFallback = fallbackAgents.filter(ua => !recentlyUsedUAs.includes(ua));
    if (filteredFallback.length === 0) {
      filteredFallback = fallbackAgents;
    }

    const selected = filteredFallback[Math.floor(Math.random() * filteredFallback.length)];

    // Track this selection
    recentlyUsedUAs.push(selected);
    if (recentlyUsedUAs.length > 5) {
      recentlyUsedUAs.shift();
    }

    console.log('Selected fallback agent:', selected);
    console.log('Recently used UAs:', recentlyUsedUAs);
    return selected;
  }

  // Filter out the current user agent and recently used ones to ensure variety
  const currentUA = document.getElementById('ua').value || navigator.userAgent;
  let filteredAgents = agents.filter(ua => ua !== currentUA && !recentlyUsedUAs.includes(ua));

  // If we've filtered out too many, just avoid the current one
  if (filteredAgents.length === 0) {
    filteredAgents = agents.filter(ua => ua !== currentUA);
  }

  // If still no options, use the full list
  if (filteredAgents.length === 0) {
    filteredAgents = agents;
  }

  const selected = filteredAgents[Math.floor(Math.random() * filteredAgents.length)];

  // Track this selection to avoid immediate repetition
  recentlyUsedUAs.push(selected);
  if (recentlyUsedUAs.length > 5) { // Keep only last 5 to avoid repetition
    recentlyUsedUAs.shift();
  }

  console.log('Selected agent from list:', selected);
  console.log('Recently used UAs:', recentlyUsedUAs);
  return selected;
}

function applyUserAgent(userAgent) {
  // Set the user agent in the input field
  document.getElementById('ua').value = userAgent;
  document.getElementById('ua').dispatchEvent(new Event('input'));

  // Apply the user agent to ALL tabs by ensuring blacklist mode with empty blacklist
  if (userAgent !== navigator.userAgent) {
    chrome.storage.local.set({
      ua: userAgent,
      mode: 'blacklist',  // Ensure blacklist mode for all tabs
      blacklist: []       // Empty blacklist means apply to all tabs
    }, () => {
      // Show success message with selected user agent info
      const selectedUA = userAgent;
      const shortUA = selectedUA.length > 50 ? selectedUA.substring(0, 50) + '...' : selectedUA;
      const successMsg = `User Agent switched to: ${shortUA}`;
      msg(successMsg);

      // Update mode status indicator
      updateModeStatus();

      // Automatically refresh the current tab to show changes immediately
      chrome.tabs.query({ active: true, currentWindow: true }, ([tab]) => {
        if (tab && tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
          chrome.tabs.reload(tab.id, { bypassCache: true });
        }
      });
    });
  }
}

function refreshAllTabs() {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
        chrome.tabs.reload(tab.id, { bypassCache: true });
      }
    });
  });
}

function autoSwitchUserAgent() {
  if (isAutoSwitching) return;

  isAutoSwitching = true;
  const btn = document.getElementById('auto-switch-btn');
  btn.classList.add('switching');

  // Check if user agent list is loaded, if not, trigger loading
  const listElement = document.getElementById('list');
  const isListLoaded = listElement && !listElement.dataset.loading &&
                      document.querySelectorAll('#list tbody tr').length > 0;

  if (!isListLoaded) {
    console.log('User agent list not loaded, triggering load...');
    // Trigger the update function to load user agents
    chrome.storage.local.get({
      ua: ''
    }, prefs => {
      // This will trigger the list to load
      update(prefs.ua || navigator.userAgent);

      // Wait a bit for the list to load, then get random UA
      setTimeout(() => {
        const randomUA = getRandomUserAgent();
        applyUserAgent(randomUA);
        finishSwitching(btn);
      }, 1000);
    });
  } else {
    // List is already loaded, get random UA immediately
    const randomUA = getRandomUserAgent();

    // Apply it after a short delay for visual feedback
    setTimeout(() => {
      applyUserAgent(randomUA);
      finishSwitching(btn);
    }, 500);
  }
}

function finishSwitching(btn) {
  // Show success state
  btn.classList.remove('switching');
  btn.classList.add('success');

  // Reset button state
  setTimeout(() => {
    btn.classList.remove('success');
    isAutoSwitching = false;
  }, 1000);
}

// Check and update mode status
function updateModeStatus() {
  chrome.storage.local.get(['mode', 'blacklist'], (prefs) => {
    const autoSwitchBtn = document.getElementById('auto-switch-btn');
    if (autoSwitchBtn) {
      // Extension is ready for all-tabs switching if in blacklist mode with empty blacklist
      const isReady = prefs.mode === 'blacklist' && (!prefs.blacklist || prefs.blacklist.length === 0);
      autoSwitchBtn.classList.toggle('mode-ready', isReady);

      // Update tooltip to show current status
      const baseTitle = autoSwitchBtn.getAttribute('title').split(' (Current')[0];
      const statusText = isReady ?
        ' (Current mode: Ready for all tabs)' :
        ` (Current mode: ${prefs.mode || 'blacklist'} - may not apply to all tabs)`;
      autoSwitchBtn.setAttribute('title', baseTitle + statusText);
    }
  });
}

// Event handler for the floating auto switch button
document.addEventListener('DOMContentLoaded', () => {
  const autoSwitchBtn = document.getElementById('auto-switch-btn');
  if (autoSwitchBtn) {
    autoSwitchBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      autoSwitchUserAgent();
    });

    // Add keyboard support (Enter and Space)
    autoSwitchBtn.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        e.stopPropagation();
        autoSwitchUserAgent();
      }
    });

    // Make button focusable
    autoSwitchBtn.setAttribute('tabindex', '0');

    // Check initial mode status
    updateModeStatus();
  }
});

// Listen for storage changes to update mode status
chrome.storage.onChanged.addListener((changes) => {
  if (changes.mode || changes.blacklist) {
    updateModeStatus();
  }
});

// Add keyboard shortcut (Ctrl+Shift+A) for auto switch
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey && e.shiftKey && e.key === 'A') {
    e.preventDefault();
    autoSwitchUserAgent();
  }
});
